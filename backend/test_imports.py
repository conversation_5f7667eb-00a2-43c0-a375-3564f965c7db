#!/usr/bin/env python
"""
Test script to verify all imports are working correctly
"""

import os
import sys
import django

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

def test_model_imports():
    """Test that all models can be imported correctly"""
    try:
        from api.models import CustomUser, AudioAnalysis, EmailVerificationToken, PasswordResetToken, Donation
        print("✅ All models imported successfully")
        
        # Test model creation (without saving)
        print(f"✅ CustomUser model: {CustomUser}")
        print(f"✅ AudioAnalysis model: {AudioAnalysis}")
        print(f"✅ EmailVerificationToken model: {EmailVerificationToken}")
        print(f"✅ PasswordResetToken model: {PasswordResetToken}")
        print(f"✅ Donation model: {Donation}")
        
        return True
    except Exception as e:
        print(f"❌ Model import error: {e}")
        return False

def test_serializer_imports():
    """Test that all serializers can be imported correctly"""
    try:
        from api.serializers import (
            UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
            AudioAnalysisSerializer, PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
            DonationSerializer, DonationCreateSerializer
        )
        print("✅ All serializers imported successfully")
        
        print(f"✅ DonationSerializer: {DonationSerializer}")
        print(f"✅ DonationCreateSerializer: {DonationCreateSerializer}")
        
        return True
    except Exception as e:
        print(f"❌ Serializer import error: {e}")
        return False

def test_view_imports():
    """Test that all views can be imported correctly"""
    try:
        from api.views import (
            UserRegistrationView, UserLoginView, UserLogoutView,
            AudioAnalysisListView, AudioAnalysisUploadView,
            DonationCreateView, DonationConfirmView, DonationListView
        )
        print("✅ All views imported successfully")
        
        print(f"✅ DonationCreateView: {DonationCreateView}")
        print(f"✅ DonationConfirmView: {DonationConfirmView}")
        print(f"✅ DonationListView: {DonationListView}")
        
        return True
    except Exception as e:
        print(f"❌ View import error: {e}")
        return False

def test_admin_imports():
    """Test that admin configurations can be imported correctly"""
    try:
        from api.admin import CustomUserAdmin, AudioAnalysisAdmin, DonationAdmin
        print("✅ All admin configurations imported successfully")
        
        print(f"✅ DonationAdmin: {DonationAdmin}")
        
        return True
    except Exception as e:
        print(f"❌ Admin import error: {e}")
        return False

def main():
    """Run all import tests"""
    print("🔍 Testing Django imports...")
    print("=" * 50)
    
    tests = [
        test_model_imports,
        test_serializer_imports,
        test_view_imports,
        test_admin_imports
    ]
    
    results = []
    for test in tests:
        print(f"\n📋 Running {test.__name__}...")
        result = test()
        results.append(result)
        print("-" * 30)
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All imports are working correctly!")
        return 0
    else:
        print("\n💥 Some imports failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
